<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语气词理解</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .keyword {
            color: #3d74ed;
            font-weight: bold;
        }
    </style>
</head>
<body class="bg-white">
    <div class="p-6">
        <h1 class="text-3xl font-bold text-gray-800 mb-6">语气词理解</h1>
        
        <div class="mb-8">
            <p class="text-gray-700 text-lg leading-relaxed mb-4">
                语气词（Discourse Markers）是英语口语和书面语中非常重要的组成部分，它们帮助说话者表达情感、态度，以及组织话语结构。掌握这些语气词的正确使用，能让你的英语表达更加自然流畅。
            </p>
        </div>

        <!-- Oh 的用法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">Oh 的多种用法</h2>
            <p class="text-gray-700 mb-4">
                "Oh" 是最常用的语气词之一，可以表达惊讶、恍然大悟、失望等多种情感。
            </p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">表示惊讶</div>
                    <div class="keyword text-lg mb-1">Oh, really?</div>
                    <div class="text-sm text-gray-600 mb-1">/oʊ ˈriːəli/</div>
                    <div class="text-gray-700">哦，真的吗？</div>
                </div>
                
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">恍然大悟</div>
                    <div class="keyword text-lg mb-1">Oh, I see!</div>
                    <div class="text-sm text-gray-600 mb-1">/oʊ aɪ siː/</div>
                    <div class="text-gray-700">哦，我明白了！</div>
                </div>
                
                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">表示失望</div>
                    <div class="keyword text-lg mb-1">Oh no, not again!</div>
                    <div class="text-sm text-gray-600 mb-1">/oʊ noʊ nɑːt əˈɡen/</div>
                    <div class="text-gray-700">哦不，又来了！</div>
                </div>
                
                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">想起某事</div>
                    <div class="keyword text-lg mb-1">Oh, by the way...</div>
                    <div class="text-sm text-gray-600 mb-1">/oʊ baɪ ðə weɪ/</div>
                    <div class="text-gray-700">哦，顺便说一下...</div>
                </div>
            </div>
        </section>

        <!-- Well 的用法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">Well 的语气功能</h2>
            <p class="text-gray-700 mb-4">
                "Well" 常用于话语开头，表示思考、犹豫、或者转换话题，是非常实用的语气词。
            </p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">表示思考</div>
                    <div class="keyword text-lg mb-1">Well, let me think...</div>
                    <div class="text-sm text-gray-600 mb-1">/wel let mi θɪŋk/</div>
                    <div class="text-gray-700">嗯，让我想想...</div>
                </div>
                
                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">表示无奈</div>
                    <div class="keyword text-lg mb-1">Well, what can I say?</div>
                    <div class="text-sm text-gray-600 mb-1">/wel wʌt kæn aɪ seɪ/</div>
                    <div class="text-gray-700">嗯，我还能说什么呢？</div>
                </div>
                
                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">转换话题</div>
                    <div class="keyword text-lg mb-1">Well, anyway...</div>
                    <div class="text-sm text-gray-600 mb-1">/wel ˈeniweɪ/</div>
                    <div class="text-gray-700">嗯，不管怎样...</div>
                </div>
                
                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">表示满意</div>
                    <div class="keyword text-lg mb-1">Well done!</div>
                    <div class="text-sm text-gray-600 mb-1">/wel dʌn/</div>
                    <div class="text-gray-700">做得好！</div>
                </div>
            </div>
        </section>

        <!-- You know 的用法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">You know 的交际功能</h2>
            <p class="text-gray-700 mb-4">
                "You know" 是口语中极其常见的语气词，用于寻求理解、填补停顿，或强调某个观点。
            </p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">寻求认同</div>
                    <div class="keyword text-lg mb-1">It's hard, you know?</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪts hɑːrd ju noʊ/</div>
                    <div class="text-gray-700">这很难，你知道的？</div>
                </div>
                
                <div class="card bg-gray-50 border border-gray-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">填补停顿</div>
                    <div class="keyword text-lg mb-1">I was, you know, thinking...</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ wʌz ju noʊ θɪŋkɪŋ/</div>
                    <div class="text-gray-700">我在，你知道的，思考...</div>
                </div>
                
                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">强调观点</div>
                    <div class="keyword text-lg mb-1">You know what I mean?</div>
                    <div class="text-sm text-gray-600 mb-1">/ju noʊ wʌt aɪ miːn/</div>
                    <div class="text-gray-700">你明白我的意思吗？</div>
                </div>
                
                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">表示熟悉</div>
                    <div class="keyword text-lg mb-1">You know John, right?</div>
                    <div class="text-sm text-gray-600 mb-1">/ju noʊ dʒɑːn raɪt/</div>
                    <div class="text-gray-700">你认识约翰，对吧？</div>
                </div>
            </div>
        </section>

        <!-- I mean 的用法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">I mean 的澄清功能</h2>
            <p class="text-gray-700 mb-4">
                "I mean" 主要用于澄清、解释或修正之前说过的话，是表达准确意思的重要工具。
            </p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">澄清意思</div>
                    <div class="keyword text-lg mb-1">It's good, I mean, really good.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪts ɡʊd aɪ miːn ˈriːəli ɡʊd/</div>
                    <div class="text-gray-700">这很好，我是说，真的很好。</div>
                </div>
                
                <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">修正表达</div>
                    <div class="keyword text-lg mb-1">He's nice, I mean, kind.</div>
                    <div class="text-sm text-gray-600 mb-1">/hiz naɪs aɪ miːn kaɪnd/</div>
                    <div class="text-gray-700">他很好，我是说，很善良。</div>
                </div>
                
                <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">进一步解释</div>
                    <div class="keyword text-lg mb-1">I mean, what's the point?</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ miːn wʌts ðə pɔɪnt/</div>
                    <div class="text-gray-700">我是说，这有什么意义呢？</div>
                </div>
                
                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">强调重点</div>
                    <div class="keyword text-lg mb-1">I mean it!</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ miːn ɪt/</div>
                    <div class="text-gray-700">我是认真的！</div>
                </div>
            </div>
        </section>

        <!-- Like 的用法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">Like 的口语功能</h2>
            <p class="text-gray-700 mb-4">
                "Like" 在口语中不仅表示"喜欢"，更常用作语气词，表示举例、近似或填补停顿。
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">举例说明</div>
                    <div class="keyword text-lg mb-1">I like sports, like basketball.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ laɪk spɔːrts laɪk ˈbæskɪtbɔːl/</div>
                    <div class="text-gray-700">我喜欢运动，比如篮球。</div>
                </div>

                <div class="card bg-stone-50 border border-stone-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">表示近似</div>
                    <div class="keyword text-lg mb-1">It's like, really cold.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪts laɪk ˈriːəli koʊld/</div>
                    <div class="text-gray-700">天气，嗯，真的很冷。</div>
                </div>

                <div class="card bg-neutral-50 border border-neutral-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">引用话语</div>
                    <div class="keyword text-lg mb-1">She was like, "No way!"</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi wʌz laɪk noʊ weɪ/</div>
                    <div class="text-gray-700">她说，"不可能！"</div>
                </div>

                <div class="card bg-slate-50 border border-slate-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">填补停顿</div>
                    <div class="keyword text-lg mb-1">I was, like, confused.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ wʌz laɪk kənˈfjuːzd/</div>
                    <div class="text-gray-700">我当时，嗯，很困惑。</div>
                </div>
            </div>
        </section>

        <!-- Actually 的用法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">Actually 的修正功能</h2>
            <p class="text-gray-700 mb-4">
                "Actually" 用于修正、澄清或提供新信息，常常带有轻微的对比或纠正意味。
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-red-100 border border-red-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">纠正信息</div>
                    <div class="keyword text-lg mb-1">Actually, it's Tuesday.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈæktʃuəli ɪts ˈtuːzdeɪ/</div>
                    <div class="text-gray-700">实际上，今天是星期二。</div>
                </div>

                <div class="card bg-blue-100 border border-blue-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">提供新信息</div>
                    <div class="keyword text-lg mb-1">Actually, I have an idea.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈæktʃuəli aɪ hæv ən aɪˈdiə/</div>
                    <div class="text-gray-700">其实，我有个想法。</div>
                </div>

                <div class="card bg-green-100 border border-green-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">表示意外</div>
                    <div class="keyword text-lg mb-1">Actually, that's not bad.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈæktʃuəli ðæts nɑːt bæd/</div>
                    <div class="text-gray-700">其实，那还不错。</div>
                </div>

                <div class="card bg-yellow-100 border border-yellow-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">强调真实性</div>
                    <div class="keyword text-lg mb-1">I actually did it!</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ ˈæktʃuəli dɪd ɪt/</div>
                    <div class="text-gray-700">我真的做到了！</div>
                </div>
            </div>
        </section>

        <!-- So 的用法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">So 的连接功能</h2>
            <p class="text-gray-700 mb-4">
                "So" 作为语气词，常用于开始新话题、总结或表示结果，是组织话语的重要工具。
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-purple-100 border border-purple-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">开始话题</div>
                    <div class="keyword text-lg mb-1">So, what's new?</div>
                    <div class="text-sm text-gray-600 mb-1">/soʊ wʌts nuː/</div>
                    <div class="text-gray-700">那么，有什么新鲜事吗？</div>
                </div>

                <div class="card bg-pink-100 border border-pink-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">表示结果</div>
                    <div class="keyword text-lg mb-1">So, I decided to leave.</div>
                    <div class="text-sm text-gray-600 mb-1">/soʊ aɪ dɪˈsaɪdɪd tu liːv/</div>
                    <div class="text-gray-700">所以，我决定离开。</div>
                </div>

                <div class="card bg-indigo-100 border border-indigo-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">总结观点</div>
                    <div class="keyword text-lg mb-1">So, that's the plan.</div>
                    <div class="text-sm text-gray-600 mb-1">/soʊ ðæts ðə plæn/</div>
                    <div class="text-gray-700">所以，这就是计划。</div>
                </div>

                <div class="card bg-teal-100 border border-teal-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">寻求确认</div>
                    <div class="keyword text-lg mb-1">So, you agree?</div>
                    <div class="text-sm text-gray-600 mb-1">/soʊ ju əˈɡriː/</div>
                    <div class="text-gray-700">那么，你同意吗？</div>
                </div>
            </div>
        </section>

        <!-- Right 的用法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">Right 的确认功能</h2>
            <p class="text-gray-700 mb-4">
                "Right" 作为语气词，主要用于寻求确认、表示理解或强调正确性。
            </p>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="card bg-orange-100 border border-orange-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">寻求确认</div>
                    <div class="keyword text-lg mb-1">You're coming, right?</div>
                    <div class="text-sm text-gray-600 mb-1">/jʊr ˈkʌmɪŋ raɪt/</div>
                    <div class="text-gray-700">你会来的，对吧？</div>
                </div>

                <div class="card bg-emerald-100 border border-emerald-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">表示理解</div>
                    <div class="keyword text-lg mb-1">Right, I got it.</div>
                    <div class="text-sm text-gray-600 mb-1">/raɪt aɪ ɡɑːt ɪt/</div>
                    <div class="text-gray-700">对，我明白了。</div>
                </div>

                <div class="card bg-cyan-100 border border-cyan-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">开始新话题</div>
                    <div class="keyword text-lg mb-1">Right, let's start.</div>
                    <div class="text-sm text-gray-600 mb-1">/raɪt lets stɑːrt/</div>
                    <div class="text-gray-700">好，我们开始吧。</div>
                </div>
            </div>
        </section>

        <!-- Anyway 的用法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">Anyway 的转换功能</h2>
            <p class="text-gray-700 mb-4">
                "Anyway" 用于转换话题、回到主题或表示"不管怎样"的态度。
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-violet-100 border border-violet-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">转换话题</div>
                    <div class="keyword text-lg mb-1">Anyway, let's move on.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈeniweɪ lets muːv ɑːn/</div>
                    <div class="text-gray-700">不管怎样，我们继续吧。</div>
                </div>

                <div class="card bg-rose-100 border border-rose-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">回到主题</div>
                    <div class="keyword text-lg mb-1">Anyway, as I was saying...</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈeniweɪ æz aɪ wʌz ˈseɪɪŋ/</div>
                    <div class="text-gray-700">不管怎样，正如我刚才说的...</div>
                </div>

                <div class="card bg-amber-100 border border-amber-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">表示无所谓</div>
                    <div class="keyword text-lg mb-1">Anyway, it doesn't matter.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈeniweɪ ɪt ˈdʌzənt ˈmætər/</div>
                    <div class="text-gray-700">不管怎样，这无关紧要。</div>
                </div>

                <div class="card bg-lime-100 border border-lime-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">结束对话</div>
                    <div class="keyword text-lg mb-1">Anyway, I have to go.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈeniweɪ aɪ hæv tu ɡoʊ/</div>
                    <div class="text-gray-700">不管怎样，我得走了。</div>
                </div>
            </div>
        </section>

        <!-- Um/Uh 的用法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">Um/Uh 的停顿功能</h2>
            <p class="text-gray-700 mb-4">
                "Um" 和 "Uh" 是最常见的填充词，用于思考时的停顿，给说话者争取时间。
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-gray-100 border border-gray-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">思考停顿</div>
                    <div class="keyword text-lg mb-1">Um, let me see...</div>
                    <div class="text-sm text-gray-600 mb-1">/ʌm let mi siː/</div>
                    <div class="text-gray-700">嗯，让我看看...</div>
                </div>

                <div class="card bg-neutral-100 border border-neutral-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">犹豫表达</div>
                    <div class="keyword text-lg mb-1">Uh, I'm not sure.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʌ aɪm nɑːt ʃʊr/</div>
                    <div class="text-gray-700">呃，我不确定。</div>
                </div>

                <div class="card bg-slate-100 border border-slate-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">寻找词汇</div>
                    <div class="keyword text-lg mb-1">It's, um, complicated.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪts ʌm ˈkɑːmpləkeɪtɪd/</div>
                    <div class="text-gray-700">这，嗯，很复杂。</div>
                </div>

                <div class="card bg-stone-100 border border-stone-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">开始回答</div>
                    <div class="keyword text-lg mb-1">Uh, yes, that's right.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʌ jes ðæts raɪt/</div>
                    <div class="text-gray-700">呃，是的，没错。</div>
                </div>
            </div>
        </section>

        <!-- 语气词的使用技巧 -->
        <section class="mb-8">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">语气词使用技巧</h2>
            <div class="bg-gray-50 border border-gray-200 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">自然使用的要点</h3>
                <ul class="space-y-3 text-gray-700">
                    <li class="flex items-start">
                        <span class="keyword mr-2">•</span>
                        <span>适度使用：过多的语气词会让表达显得不够流畅和专业</span>
                    </li>
                    <li class="flex items-start">
                        <span class="keyword mr-2">•</span>
                        <span>语境匹配：正式场合减少使用填充词，非正式对话可以适当使用</span>
                    </li>
                    <li class="flex items-start">
                        <span class="keyword mr-2">•</span>
                        <span>语调配合：语气词的语调变化能传达不同的情感和态度</span>
                    </li>
                    <li class="flex items-start">
                        <span class="keyword mr-2">•</span>
                        <span>文化理解：不同的语气词在不同文化背景下可能有细微差别</span>
                    </li>
                    <li class="flex items-start">
                        <span class="keyword mr-2">•</span>
                        <span>练习模仿：通过听英语母语者的对话来学习自然的语气词使用</span>
                    </li>
                </ul>
            </div>
        </section>

        <!-- 常见组合用法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">语气词组合用法</h2>
            <p class="text-gray-700 mb-4">
                多个语气词经常组合使用，形成更复杂的表达效果。
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-sky-100 border border-sky-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">组合表达</div>
                    <div class="keyword text-lg mb-1">Well, you know, I mean...</div>
                    <div class="text-sm text-gray-600 mb-1">/wel ju noʊ aɪ miːn/</div>
                    <div class="text-gray-700">嗯，你知道的，我是说...</div>
                </div>

                <div class="card bg-emerald-100 border border-emerald-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">强调组合</div>
                    <div class="keyword text-lg mb-1">Oh well, anyway...</div>
                    <div class="text-sm text-gray-600 mb-1">/oʊ wel ˈeniweɪ/</div>
                    <div class="text-gray-700">哦好吧，不管怎样...</div>
                </div>

                <div class="card bg-purple-100 border border-purple-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">思考组合</div>
                    <div class="keyword text-lg mb-1">Um, actually, you know what?</div>
                    <div class="text-sm text-gray-600 mb-1">/ʌm ˈæktʃuəli ju noʊ wʌt/</div>
                    <div class="text-gray-700">嗯，其实，你知道吗？</div>
                </div>

                <div class="card bg-pink-100 border border-pink-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">确认组合</div>
                    <div class="keyword text-lg mb-1">So, right, that's it.</div>
                    <div class="text-sm text-gray-600 mb-1">/soʊ raɪt ðæts ɪt/</div>
                    <div class="text-gray-700">所以，对，就是这样。</div>
                </div>
            </div>
        </section>
    </div>
</body>
</html>
