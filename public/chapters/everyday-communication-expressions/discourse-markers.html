<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语气词理解</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .keyword {
            color: #3d74ed;
            font-weight: bold;
        }
    </style>
</head>
<body class="bg-white">
    <div class="p-6">
        <h1 class="text-3xl font-bold text-gray-800 mb-6">语气词理解</h1>
        
        <div class="mb-8">
            <p class="text-gray-700 text-lg leading-relaxed mb-4">
                语气词（Discourse Markers）是英语口语和书面语中非常重要的组成部分，它们帮助说话者表达情感、态度，以及组织话语结构。掌握这些语气词的正确使用，能让你的英语表达更加自然流畅。
            </p>
        </div>

        <!-- Oh 的用法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">Oh 的多种用法</h2>
            <p class="text-gray-700 mb-4">
                "Oh" 是最常用的语气词之一，可以表达惊讶、恍然大悟、失望等多种情感。
            </p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">表示惊讶</div>
                    <div class="keyword text-lg mb-1">Oh, really?</div>
                    <div class="text-sm text-gray-600 mb-1">/oʊ ˈriːəli/</div>
                    <div class="text-gray-700">哦，真的吗？</div>
                </div>
                
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">恍然大悟</div>
                    <div class="keyword text-lg mb-1">Oh, I see!</div>
                    <div class="text-sm text-gray-600 mb-1">/oʊ aɪ siː/</div>
                    <div class="text-gray-700">哦，我明白了！</div>
                </div>
                
                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">表示失望</div>
                    <div class="keyword text-lg mb-1">Oh no, not again!</div>
                    <div class="text-sm text-gray-600 mb-1">/oʊ noʊ nɑːt əˈɡen/</div>
                    <div class="text-gray-700">哦不，又来了！</div>
                </div>
                
                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">想起某事</div>
                    <div class="keyword text-lg mb-1">Oh, by the way...</div>
                    <div class="text-sm text-gray-600 mb-1">/oʊ baɪ ðə weɪ/</div>
                    <div class="text-gray-700">哦，顺便说一下...</div>
                </div>
            </div>
        </section>

        <!-- Well 的用法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">Well 的语气功能</h2>
            <p class="text-gray-700 mb-4">
                "Well" 常用于话语开头，表示思考、犹豫、或者转换话题，是非常实用的语气词。
            </p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">表示思考</div>
                    <div class="keyword text-lg mb-1">Well, let me think...</div>
                    <div class="text-sm text-gray-600 mb-1">/wel let mi θɪŋk/</div>
                    <div class="text-gray-700">嗯，让我想想...</div>
                </div>
                
                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">表示无奈</div>
                    <div class="keyword text-lg mb-1">Well, what can I say?</div>
                    <div class="text-sm text-gray-600 mb-1">/wel wʌt kæn aɪ seɪ/</div>
                    <div class="text-gray-700">嗯，我还能说什么呢？</div>
                </div>
                
                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">转换话题</div>
                    <div class="keyword text-lg mb-1">Well, anyway...</div>
                    <div class="text-sm text-gray-600 mb-1">/wel ˈeniweɪ/</div>
                    <div class="text-gray-700">嗯，不管怎样...</div>
                </div>
                
                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">表示满意</div>
                    <div class="keyword text-lg mb-1">Well done!</div>
                    <div class="text-sm text-gray-600 mb-1">/wel dʌn/</div>
                    <div class="text-gray-700">做得好！</div>
                </div>
            </div>
        </section>

        <!-- You know 的用法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">You know 的交际功能</h2>
            <p class="text-gray-700 mb-4">
                "You know" 是口语中极其常见的语气词，用于寻求理解、填补停顿，或强调某个观点。
            </p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">寻求认同</div>
                    <div class="keyword text-lg mb-1">It's hard, you know?</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪts hɑːrd ju noʊ/</div>
                    <div class="text-gray-700">这很难，你知道的？</div>
                </div>
                
                <div class="card bg-gray-50 border border-gray-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">填补停顿</div>
                    <div class="keyword text-lg mb-1">I was, you know, thinking...</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ wʌz ju noʊ θɪŋkɪŋ/</div>
                    <div class="text-gray-700">我在，你知道的，思考...</div>
                </div>
                
                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">强调观点</div>
                    <div class="keyword text-lg mb-1">You know what I mean?</div>
                    <div class="text-sm text-gray-600 mb-1">/ju noʊ wʌt aɪ miːn/</div>
                    <div class="text-gray-700">你明白我的意思吗？</div>
                </div>
                
                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">表示熟悉</div>
                    <div class="keyword text-lg mb-1">You know John, right?</div>
                    <div class="text-sm text-gray-600 mb-1">/ju noʊ dʒɑːn raɪt/</div>
                    <div class="text-gray-700">你认识约翰，对吧？</div>
                </div>
            </div>
        </section>

        <!-- I mean 的用法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">I mean 的澄清功能</h2>
            <p class="text-gray-700 mb-4">
                "I mean" 主要用于澄清、解释或修正之前说过的话，是表达准确意思的重要工具。
            </p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">澄清意思</div>
                    <div class="keyword text-lg mb-1">It's good, I mean, really good.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪts ɡʊd aɪ miːn ˈriːəli ɡʊd/</div>
                    <div class="text-gray-700">这很好，我是说，真的很好。</div>
                </div>
                
                <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">修正表达</div>
                    <div class="keyword text-lg mb-1">He's nice, I mean, kind.</div>
                    <div class="text-sm text-gray-600 mb-1">/hiz naɪs aɪ miːn kaɪnd/</div>
                    <div class="text-gray-700">他很好，我是说，很善良。</div>
                </div>
                
                <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">进一步解释</div>
                    <div class="keyword text-lg mb-1">I mean, what's the point?</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ miːn wʌts ðə pɔɪnt/</div>
                    <div class="text-gray-700">我是说，这有什么意义呢？</div>
                </div>
                
                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">强调重点</div>
                    <div class="keyword text-lg mb-1">I mean it!</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ miːn ɪt/</div>
                    <div class="text-gray-700">我是认真的！</div>
                </div>
            </div>
        </section>

        <!-- Like 的用法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">Like 的口语功能</h2>
            <p class="text-gray-700 mb-4">
                "Like" 在口语中不仅表示"喜欢"，更常用作语气词，表示举例、近似或填补停顿。
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">举例说明</div>
                    <div class="keyword text-lg mb-1">I like sports, like basketball.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ laɪk spɔːrts laɪk ˈbæskɪtbɔːl/</div>
                    <div class="text-gray-700">我喜欢运动，比如篮球。</div>
                </div>

                <div class="card bg-stone-50 border border-stone-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">表示近似</div>
                    <div class="keyword text-lg mb-1">It's like, really cold.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪts laɪk ˈriːəli koʊld/</div>
                    <div class="text-gray-700">天气，嗯，真的很冷。</div>
                </div>

                <div class="card bg-neutral-50 border border-neutral-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">引用话语</div>
                    <div class="keyword text-lg mb-1">She was like, "No way!"</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi wʌz laɪk noʊ weɪ/</div>
                    <div class="text-gray-700">她说，"不可能！"</div>
                </div>

                <div class="card bg-slate-50 border border-slate-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">填补停顿</div>
                    <div class="keyword text-lg mb-1">I was, like, confused.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ wʌz laɪk kənˈfjuːzd/</div>
                    <div class="text-gray-700">我当时，嗯，很困惑。</div>
                </div>
            </div>
        </section>

        <!-- Actually 的用法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">Actually 的修正功能</h2>
            <p class="text-gray-700 mb-4">
                "Actually" 用于修正、澄清或提供新信息，常常带有轻微的对比或纠正意味。
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-red-100 border border-red-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">纠正信息</div>
                    <div class="keyword text-lg mb-1">Actually, it's Tuesday.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈæktʃuəli ɪts ˈtuːzdeɪ/</div>
                    <div class="text-gray-700">实际上，今天是星期二。</div>
                </div>

                <div class="card bg-blue-100 border border-blue-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">提供新信息</div>
                    <div class="keyword text-lg mb-1">Actually, I have an idea.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈæktʃuəli aɪ hæv ən aɪˈdiə/</div>
                    <div class="text-gray-700">其实，我有个想法。</div>
                </div>

                <div class="card bg-green-100 border border-green-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">表示意外</div>
                    <div class="keyword text-lg mb-1">Actually, that's not bad.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈæktʃuəli ðæts nɑːt bæd/</div>
                    <div class="text-gray-700">其实，那还不错。</div>
                </div>

                <div class="card bg-yellow-100 border border-yellow-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">强调真实性</div>
                    <div class="keyword text-lg mb-1">I actually did it!</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ ˈæktʃuəli dɪd ɪt/</div>
                    <div class="text-gray-700">我真的做到了！</div>
                </div>
            </div>
        </section>

        <!-- So 的用法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">So 的连接功能</h2>
            <p class="text-gray-700 mb-4">
                "So" 作为语气词，常用于开始新话题、总结或表示结果，是组织话语的重要工具。
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-purple-100 border border-purple-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">开始话题</div>
                    <div class="keyword text-lg mb-1">So, what's new?</div>
                    <div class="text-sm text-gray-600 mb-1">/soʊ wʌts nuː/</div>
                    <div class="text-gray-700">那么，有什么新鲜事吗？</div>
                </div>

                <div class="card bg-pink-100 border border-pink-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">表示结果</div>
                    <div class="keyword text-lg mb-1">So, I decided to leave.</div>
                    <div class="text-sm text-gray-600 mb-1">/soʊ aɪ dɪˈsaɪdɪd tu liːv/</div>
                    <div class="text-gray-700">所以，我决定离开。</div>
                </div>

                <div class="card bg-indigo-100 border border-indigo-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">总结观点</div>
                    <div class="keyword text-lg mb-1">So, that's the plan.</div>
                    <div class="text-sm text-gray-600 mb-1">/soʊ ðæts ðə plæn/</div>
                    <div class="text-gray-700">所以，这就是计划。</div>
                </div>

                <div class="card bg-teal-100 border border-teal-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">寻求确认</div>
                    <div class="keyword text-lg mb-1">So, you agree?</div>
                    <div class="text-sm text-gray-600 mb-1">/soʊ ju əˈɡriː/</div>
                    <div class="text-gray-700">那么，你同意吗？</div>
                </div>
            </div>
        </section>

        <!-- Right 的用法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">Right 的确认功能</h2>
            <p class="text-gray-700 mb-4">
                "Right" 作为语气词，主要用于寻求确认、表示理解或强调正确性。
            </p>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="card bg-orange-100 border border-orange-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">寻求确认</div>
                    <div class="keyword text-lg mb-1">You're coming, right?</div>
                    <div class="text-sm text-gray-600 mb-1">/jʊr ˈkʌmɪŋ raɪt/</div>
                    <div class="text-gray-700">你会来的，对吧？</div>
                </div>

                <div class="card bg-emerald-100 border border-emerald-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">表示理解</div>
                    <div class="keyword text-lg mb-1">Right, I got it.</div>
                    <div class="text-sm text-gray-600 mb-1">/raɪt aɪ ɡɑːt ɪt/</div>
                    <div class="text-gray-700">对，我明白了。</div>
                </div>

                <div class="card bg-cyan-100 border border-cyan-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">开始新话题</div>
                    <div class="keyword text-lg mb-1">Right, let's start.</div>
                    <div class="text-sm text-gray-600 mb-1">/raɪt lets stɑːrt/</div>
                    <div class="text-gray-700">好，我们开始吧。</div>
                </div>
            </div>
        </section>

        <!-- Anyway 的用法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">Anyway 的转换功能</h2>
            <p class="text-gray-700 mb-4">
                "Anyway" 用于转换话题、回到主题或表示"不管怎样"的态度。
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-violet-100 border border-violet-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">转换话题</div>
                    <div class="keyword text-lg mb-1">Anyway, let's move on.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈeniweɪ lets muːv ɑːn/</div>
                    <div class="text-gray-700">不管怎样，我们继续吧。</div>
                </div>

                <div class="card bg-rose-100 border border-rose-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">回到主题</div>
                    <div class="keyword text-lg mb-1">Anyway, as I was saying...</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈeniweɪ æz aɪ wʌz ˈseɪɪŋ/</div>
                    <div class="text-gray-700">不管怎样，正如我刚才说的...</div>
                </div>

                <div class="card bg-amber-100 border border-amber-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">表示无所谓</div>
                    <div class="keyword text-lg mb-1">Anyway, it doesn't matter.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈeniweɪ ɪt ˈdʌzənt ˈmætər/</div>
                    <div class="text-gray-700">不管怎样，这无关紧要。</div>
                </div>

                <div class="card bg-lime-100 border border-lime-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">结束对话</div>
                    <div class="keyword text-lg mb-1">Anyway, I have to go.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈeniweɪ aɪ hæv tu ɡoʊ/</div>
                    <div class="text-gray-700">不管怎样，我得走了。</div>
                </div>
            </div>
        </section>

        <!-- Um/Uh 的用法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">Um/Uh 的停顿功能</h2>
            <p class="text-gray-700 mb-4">
                "Um" 和 "Uh" 是最常见的填充词，用于思考时的停顿，给说话者争取时间。
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-gray-100 border border-gray-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">思考停顿</div>
                    <div class="keyword text-lg mb-1">Um, let me see...</div>
                    <div class="text-sm text-gray-600 mb-1">/ʌm let mi siː/</div>
                    <div class="text-gray-700">嗯，让我看看...</div>
                </div>

                <div class="card bg-neutral-100 border border-neutral-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">犹豫表达</div>
                    <div class="keyword text-lg mb-1">Uh, I'm not sure.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʌ aɪm nɑːt ʃʊr/</div>
                    <div class="text-gray-700">呃，我不确定。</div>
                </div>

                <div class="card bg-slate-100 border border-slate-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">寻找词汇</div>
                    <div class="keyword text-lg mb-1">It's, um, complicated.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪts ʌm ˈkɑːmpləkeɪtɪd/</div>
                    <div class="text-gray-700">这，嗯，很复杂。</div>
                </div>

                <div class="card bg-stone-100 border border-stone-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">开始回答</div>
                    <div class="keyword text-lg mb-1">Uh, yes, that's right.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʌ jes ðæts raɪt/</div>
                    <div class="text-gray-700">呃，是的，没错。</div>
                </div>
            </div>
        </section>

        <!-- 语气词的使用技巧 -->
        <section class="mb-8">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">语气词使用技巧</h2>
            <div class="bg-gray-50 border border-gray-200 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">自然使用的要点</h3>
                <ul class="space-y-3 text-gray-700">
                    <li class="flex items-start">
                        <span class="keyword mr-2">•</span>
                        <span>适度使用：过多的语气词会让表达显得不够流畅和专业</span>
                    </li>
                    <li class="flex items-start">
                        <span class="keyword mr-2">•</span>
                        <span>语境匹配：正式场合减少使用填充词，非正式对话可以适当使用</span>
                    </li>
                    <li class="flex items-start">
                        <span class="keyword mr-2">•</span>
                        <span>语调配合：语气词的语调变化能传达不同的情感和态度</span>
                    </li>
                    <li class="flex items-start">
                        <span class="keyword mr-2">•</span>
                        <span>文化理解：不同的语气词在不同文化背景下可能有细微差别</span>
                    </li>
                    <li class="flex items-start">
                        <span class="keyword mr-2">•</span>
                        <span>练习模仿：通过听英语母语者的对话来学习自然的语气词使用</span>
                    </li>
                </ul>
            </div>
        </section>

        <!-- 常见组合用法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">语气词组合用法</h2>
            <p class="text-gray-700 mb-4">
                多个语气词经常组合使用，形成更复杂的表达效果。
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-sky-100 border border-sky-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">组合表达</div>
                    <div class="keyword text-lg mb-1">Well, you know, I mean...</div>
                    <div class="text-sm text-gray-600 mb-1">/wel ju noʊ aɪ miːn/</div>
                    <div class="text-gray-700">嗯，你知道的，我是说...</div>
                </div>

                <div class="card bg-emerald-100 border border-emerald-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">强调组合</div>
                    <div class="keyword text-lg mb-1">Oh well, anyway...</div>
                    <div class="text-sm text-gray-600 mb-1">/oʊ wel ˈeniweɪ/</div>
                    <div class="text-gray-700">哦好吧，不管怎样...</div>
                </div>

                <div class="card bg-purple-100 border border-purple-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">思考组合</div>
                    <div class="keyword text-lg mb-1">Um, actually, you know what?</div>
                    <div class="text-sm text-gray-600 mb-1">/ʌm ˈæktʃuəli ju noʊ wʌt/</div>
                    <div class="text-gray-700">嗯，其实，你知道吗？</div>
                </div>

                <div class="card bg-pink-100 border border-pink-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">确认组合</div>
                    <div class="keyword text-lg mb-1">So, right, that's it.</div>
                    <div class="text-sm text-gray-600 mb-1">/soʊ raɪt ðæts ɪt/</div>
                    <div class="text-gray-700">所以，对，就是这样。</div>
                </div>
            </div>
        </section>

        <!-- Basically 的用法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">Basically 的简化功能</h2>
            <p class="text-gray-700 mb-4">
                "Basically" 用于简化复杂概念，表示"基本上"、"简单来说"的意思。
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">简化解释</div>
                    <div class="keyword text-lg mb-1">Basically, it's simple.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈbeɪsɪkli ɪts ˈsɪmpəl/</div>
                    <div class="text-gray-700">基本上，这很简单。</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">概括总结</div>
                    <div class="keyword text-lg mb-1">Basically, we need more time.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈbeɪsɪkli wi niːd mɔːr taɪm/</div>
                    <div class="text-gray-700">基本上，我们需要更多时间。</div>
                </div>

                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">核心观点</div>
                    <div class="keyword text-lg mb-1">Basically, that's the point.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈbeɪsɪkli ðæts ðə pɔɪnt/</div>
                    <div class="text-gray-700">基本上，这就是重点。</div>
                </div>

                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">本质说明</div>
                    <div class="keyword text-lg mb-1">Basically, it's about trust.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈbeɪsɪkli ɪts əˈbaʊt trʌst/</div>
                    <div class="text-gray-700">基本上，这关乎信任。</div>
                </div>
            </div>
        </section>

        <!-- Obviously 的用法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">Obviously 的明显性表达</h2>
            <p class="text-gray-700 mb-4">
                "Obviously" 表示某事显而易见，常用于强调明显的事实或逻辑。
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">显而易见</div>
                    <div class="keyword text-lg mb-1">Obviously, it's raining.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈɑːbviəsli ɪts ˈreɪnɪŋ/</div>
                    <div class="text-gray-700">显然，在下雨。</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">理所当然</div>
                    <div class="keyword text-lg mb-1">Obviously, we should help.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈɑːbviəsli wi ʃʊd help/</div>
                    <div class="text-gray-700">显然，我们应该帮忙。</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">逻辑推理</div>
                    <div class="keyword text-lg mb-1">Obviously, he's not coming.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈɑːbviəsli hiz nɑːt ˈkʌmɪŋ/</div>
                    <div class="text-gray-700">显然，他不会来了。</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">强调事实</div>
                    <div class="keyword text-lg mb-1">Obviously, I was wrong.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈɑːbviəsli aɪ wʌz rɔːŋ/</div>
                    <div class="text-gray-700">显然，我错了。</div>
                </div>
            </div>
        </section>

        <!-- Seriously 的用法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">Seriously 的强调功能</h2>
            <p class="text-gray-700 mb-4">
                "Seriously" 用于强调认真程度，表示"认真地"、"真的"或表达不满。
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">强调认真</div>
                    <div class="keyword text-lg mb-1">Seriously, I need help.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈsɪriəsli aɪ niːd help/</div>
                    <div class="text-gray-700">说真的，我需要帮助。</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">表示惊讶</div>
                    <div class="keyword text-lg mb-1">Seriously? That's amazing!</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈsɪriəsli ðæts əˈmeɪzɪŋ/</div>
                    <div class="text-gray-700">真的吗？太棒了！</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">表达不满</div>
                    <div class="keyword text-lg mb-1">Seriously, stop it!</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈsɪriəsli stɑːp ɪt/</div>
                    <div class="text-gray-700">说真的，停下来！</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">确认真实</div>
                    <div class="keyword text-lg mb-1">I'm seriously considering it.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪm ˈsɪriəsli kənˈsɪdərɪŋ ɪt/</div>
                    <div class="text-gray-700">我在认真考虑这件事。</div>
                </div>
            </div>
        </section>

        <!-- Honestly 的用法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">Honestly 的坦诚表达</h2>
            <p class="text-gray-700 mb-4">
                "Honestly" 表示坦诚、诚实，常用于表达真实想法或强调诚意。
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">坦诚表达</div>
                    <div class="keyword text-lg mb-1">Honestly, I don't know.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈɑːnɪstli aɪ doʊnt noʊ/</div>
                    <div class="text-gray-700">说实话，我不知道。</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">真实想法</div>
                    <div class="keyword text-lg mb-1">Honestly, it's not that good.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈɑːnɪstli ɪts nɑːt ðæt ɡʊd/</div>
                    <div class="text-gray-700">说实话，这不太好。</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">强调诚意</div>
                    <div class="keyword text-lg mb-1">Honestly, I'm trying my best.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈɑːnɪstli aɪm ˈtraɪɪŋ maɪ best/</div>
                    <div class="text-gray-700">说实话，我在尽力而为。</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">表达不满</div>
                    <div class="keyword text-lg mb-1">Honestly, this is ridiculous!</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈɑːnɪstli ðɪs ɪz rɪˈdɪkjələs/</div>
                    <div class="text-gray-700">说实话，这太荒谬了！</div>
                </div>
            </div>
        </section>

        <!-- Listen 的用法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">Listen 的注意力引导</h2>
            <p class="text-gray-700 mb-4">
                "Listen" 用于引起注意，强调重要信息，或表达强烈情感。
            </p>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">引起注意</div>
                    <div class="keyword text-lg mb-1">Listen, this is important.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈlɪsən ðɪs ɪz ɪmˈpɔːrtənt/</div>
                    <div class="text-gray-700">听着，这很重要。</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">表达不满</div>
                    <div class="keyword text-lg mb-1">Listen, I've had enough!</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈlɪsən aɪv hæd ɪˈnʌf/</div>
                    <div class="text-gray-700">听着，我受够了！</div>
                </div>

                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">提出建议</div>
                    <div class="keyword text-lg mb-1">Listen, why don't we...</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈlɪsən waɪ doʊnt wi/</div>
                    <div class="text-gray-700">听着，我们为什么不...</div>
                </div>
            </div>
        </section>

        <!-- Look 的用法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">Look 的引导功能</h2>
            <p class="text-gray-700 mb-4">
                "Look" 用于引导注意力，提出观点或表达情感，类似于"听着"、"看"。
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">提出观点</div>
                    <div class="keyword text-lg mb-1">Look, here's the thing...</div>
                    <div class="text-sm text-gray-600 mb-1">/lʊk hɪrz ðə θɪŋ/</div>
                    <div class="text-gray-700">看，事情是这样的...</div>
                </div>

                <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">表达不耐烦</div>
                    <div class="keyword text-lg mb-1">Look, I don't have time!</div>
                    <div class="text-sm text-gray-600 mb-1">/lʊk aɪ doʊnt hæv taɪm/</div>
                    <div class="text-gray-700">看，我没有时间！</div>
                </div>

                <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">寻求理解</div>
                    <div class="keyword text-lg mb-1">Look, you understand, right?</div>
                    <div class="text-sm text-gray-600 mb-1">/lʊk ju ˌʌndərˈstænd raɪt/</div>
                    <div class="text-gray-700">看，你理解的，对吧？</div>
                </div>

                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">解释情况</div>
                    <div class="keyword text-lg mb-1">Look, it's complicated.</div>
                    <div class="text-sm text-gray-600 mb-1">/lʊk ɪts ˈkɑːmpləkeɪtɪd/</div>
                    <div class="text-gray-700">看，这很复杂。</div>
                </div>
            </div>
        </section>

        <!-- Okay/OK 的用法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">Okay/OK 的多功能表达</h2>
            <p class="text-gray-700 mb-4">
                "Okay" 或 "OK" 是最常用的语气词之一，可以表示同意、理解、开始新话题等。
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">表示同意</div>
                    <div class="keyword text-lg mb-1">Okay, I'll do it.</div>
                    <div class="text-sm text-gray-600 mb-1">/oʊˈkeɪ aɪl duː ɪt/</div>
                    <div class="text-gray-700">好的，我会做的。</div>
                </div>

                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">开始新话题</div>
                    <div class="keyword text-lg mb-1">OK, let's talk about...</div>
                    <div class="text-sm text-gray-600 mb-1">/oʊˈkeɪ lets tɔːk əˈbaʊt/</div>
                    <div class="text-gray-700">好，我们来谈谈...</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">表示理解</div>
                    <div class="keyword text-lg mb-1">Okay, I get it now.</div>
                    <div class="text-sm text-gray-600 mb-1">/oʊˈkeɪ aɪ ɡet ɪt naʊ/</div>
                    <div class="text-gray-700">好的，我现在明白了。</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">结束对话</div>
                    <div class="keyword text-lg mb-1">OK, see you later.</div>
                    <div class="text-sm text-gray-600 mb-1">/oʊˈkeɪ siː ju ˈleɪtər/</div>
                    <div class="text-gray-700">好的，回头见。</div>
                </div>
            </div>
        </section>

        <!-- Alright 的用法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">Alright 的确认功能</h2>
            <p class="text-gray-700 mb-4">
                "Alright" 表示"好的"、"行"，常用于确认、同意或开始行动。
            </p>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">表示同意</div>
                    <div class="keyword text-lg mb-1">Alright, let's go!</div>
                    <div class="text-sm text-gray-600 mb-1">/ɔːlˈraɪt lets ɡoʊ/</div>
                    <div class="text-gray-700">好的，我们走！</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">询问状况</div>
                    <div class="keyword text-lg mb-1">Are you alright?</div>
                    <div class="text-sm text-gray-600 mb-1">/ɑːr ju ɔːlˈraɪt/</div>
                    <div class="text-gray-700">你还好吗？</div>
                </div>

                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">开始行动</div>
                    <div class="keyword text-lg mb-1">Alright, here we go.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɔːlˈraɪt hɪr wi ɡoʊ/</div>
                    <div class="text-gray-700">好的，开始了。</div>
                </div>
            </div>
        </section>

        <!-- Yeah/Yes 的语气用法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">Yeah/Yes 的语气变化</h2>
            <p class="text-gray-700 mb-4">
                "Yeah" 和 "Yes" 不仅表示"是"，在不同语调下还能表达各种情感和态度。
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">热情同意</div>
                    <div class="keyword text-lg mb-1">Yeah, absolutely!</div>
                    <div class="text-sm text-gray-600 mb-1">/jeə ˌæbsəˈluːtli/</div>
                    <div class="text-gray-700">是的，绝对是！</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">勉强同意</div>
                    <div class="keyword text-lg mb-1">Yeah, I guess so.</div>
                    <div class="text-sm text-gray-600 mb-1">/jeə aɪ ɡes soʊ/</div>
                    <div class="text-gray-700">是的，我想是的。</div>
                </div>

                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">表示理解</div>
                    <div class="keyword text-lg mb-1">Yeah, I know what you mean.</div>
                    <div class="text-sm text-gray-600 mb-1">/jeə aɪ noʊ wʌt ju miːn/</div>
                    <div class="text-gray-700">是的，我明白你的意思。</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">表示不耐烦</div>
                    <div class="keyword text-lg mb-1">Yeah, yeah, whatever.</div>
                    <div class="text-sm text-gray-600 mb-1">/jeə jeə wʌˈtevər/</div>
                    <div class="text-gray-700">是是是，随便。</div>
                </div>
            </div>
        </section>

        <!-- No way 的用法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">No way 的强烈表达</h2>
            <p class="text-gray-700 mb-4">
                "No way" 表示强烈的否定、惊讶或不相信，是非常有力的语气表达。
            </p>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">强烈拒绝</div>
                    <div class="keyword text-lg mb-1">No way! I won't do it.</div>
                    <div class="text-sm text-gray-600 mb-1">/noʊ weɪ aɪ woʊnt duː ɪt/</div>
                    <div class="text-gray-700">不可能！我不会做的。</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">表示惊讶</div>
                    <div class="keyword text-lg mb-1">No way! Really?</div>
                    <div class="text-sm text-gray-600 mb-1">/noʊ weɪ ˈriːəli/</div>
                    <div class="text-gray-700">不可能！真的吗？</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">不相信</div>
                    <div class="keyword text-lg mb-1">No way that's true!</div>
                    <div class="text-sm text-gray-600 mb-1">/noʊ weɪ ðæts truː/</div>
                    <div class="text-gray-700">不可能是真的！</div>
                </div>
            </div>
        </section>

        <!-- 情境化语气词使用 -->
        <section class="mb-8">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">不同情境下的语气词选择</h2>
            <div class="space-y-6">
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4 keyword">正式场合</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="card bg-white border border-gray-200 rounded-lg p-3 shadow-sm">
                            <div class="keyword text-base mb-1">Actually, I believe...</div>
                            <div class="text-sm text-gray-600">实际上，我认为...</div>
                        </div>
                        <div class="card bg-white border border-gray-200 rounded-lg p-3 shadow-sm">
                            <div class="keyword text-base mb-1">Obviously, this shows...</div>
                            <div class="text-sm text-gray-600">显然，这表明...</div>
                        </div>
                    </div>
                </div>

                <div class="bg-green-50 border border-green-200 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4 keyword">日常对话</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="card bg-white border border-gray-200 rounded-lg p-3 shadow-sm">
                            <div class="keyword text-base mb-1">You know, I was thinking...</div>
                            <div class="text-sm text-gray-600">你知道，我在想...</div>
                        </div>
                        <div class="card bg-white border border-gray-200 rounded-lg p-3 shadow-sm">
                            <div class="keyword text-base mb-1">Like, whatever, right?</div>
                            <div class="text-sm text-gray-600">就像，随便，对吧？</div>
                        </div>
                    </div>
                </div>

                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4 keyword">情感表达</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="card bg-white border border-gray-200 rounded-lg p-3 shadow-sm">
                            <div class="keyword text-base mb-1">Seriously, I'm so tired!</div>
                            <div class="text-sm text-gray-600">说真的，我太累了！</div>
                        </div>
                        <div class="card bg-white border border-gray-200 rounded-lg p-3 shadow-sm">
                            <div class="keyword text-base mb-1">Oh my god, that's crazy!</div>
                            <div class="text-sm text-gray-600">天哪，太疯狂了！</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
</body>
</html>
